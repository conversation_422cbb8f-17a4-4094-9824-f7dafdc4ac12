package com.hengrui.medcoding.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.fastjson.JSON;
import com.hengrui.medcoding.constant.MedCN;
import com.hengrui.medcoding.constant.SASOnlieConstant;
import com.hengrui.medcoding.constant.WhoCN;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.hengrui.medcoding.service.DownloadMedcoding.TEMP_FILE_PATH;

@Slf4j
@Component
public class FilesUtil {

    /**
     * 文件对象转MultipartFile对象
     * @param file 文件
     * @return MultipartFile对象
     */
    public static MultipartFile fileToMultipartFile(File file) {
        FileItem fileItem = createFileItem(file);
        return new CommonsMultipartFile(fileItem);
    }

    /**
     * 根据File对象创建FileItem对象
     * @param file File
     * @return FileItem对象
     */
    public static FileItem createFileItem(File file) {
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        FileItem item = factory.createItem(file.getName(), "text/plain", true, file.getName());
        int bytesRead = 0;
        byte[] buffer = new byte[8192];
        try {
            FileInputStream fis = new FileInputStream(file);
            OutputStream os = item.getOutputStream();
            while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            fis.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return item;
    }


    public static void getCDTMSAPI(String server) {
        if (server.contains("meduap-tst")) {
            SASOnlieConstant.setRemoteServerApi("https://meduap-tst.hengrui.com:8085/");
        } else if (server.contains("cdtms-val")) {
            SASOnlieConstant.setRemoteServerApi("https://cdtms-val.hengrui.com/");
        }
    }


    public static void getCDTMSAPIProjectId(String server) {
        if (server.contains("meduap-tst")) {
            SASOnlieConstant.setRemoteServerProjectid("cdtmsen_val");
        } else if (server.contains("cdtms-val")) {
            SASOnlieConstant.setRemoteServerProjectid("cdtmsen_val2");
        }else if(server.contains("cdtms")&&!server.contains("cdtms-val")){
            SASOnlieConstant.setCdtmsCheckLoginApi("https://cdtms.hengrui.com/extdatabind.checktoken.do?token=");
        }
    }

    public static void getCDTMSAPIPre(String server) {
        if (server.contains("meduap-tst")) {
            SASOnlieConstant.setRemoteServerApiPrefix("meduap-tst.hengrui.com:8085/");
        } else if (server.contains("cdtms-val")) {
            SASOnlieConstant.setRemoteServerApiPrefix("cdtms-val.hengrui.com/");
        }
    }


    public static String fiilterMedFile(String codingMethod,String filePath){
        String inputFile =filePath;
        // 读取 CSV 文件
        List<MedCN> dataList = new ArrayList<>();
        EasyExcel.read(inputFile, MedCN.class, new PageReadListener<MedCN>(dataList::addAll))
                .sheet()
                .doRead();

        // 筛选 Coding Method 列为 "同义词库(global)" 的数据
        List<MedCN> filteredData = dataList.stream()
                .filter(data -> codingMethod.equals(data.getCodingMethod()))
                .collect(Collectors.toList());

        // 判断是否找到匹配的数据
        if (filteredData.isEmpty()) {
            return "not found";
        } else {
            // 将筛选后的数据写入文件
            EasyExcel.write(inputFile, MedCN.class)
                    .sheet("Filtered Data")
                    .doWrite(filteredData);
            return "found";
        }
    }


    public static String fiilterWhoFile(String codingMethod,String filePath){
        String inputFile =filePath;
        // 读取 CSV 文件
        List<WhoCN> dataList = new ArrayList<>();
        EasyExcel.read(inputFile, WhoCN.class, new PageReadListener<WhoCN>(dataList::addAll))
                .sheet()
                .doRead();


        // 筛选 Coding Method 列为 "同义词库(global)" 的数据
        List<WhoCN> filteredData = dataList.stream()
                .filter(data -> codingMethod.equals(data.getCodingMethod()))
                .collect(Collectors.toList());
        // 判断是否找到匹配的数据
        if (filteredData.isEmpty()) {
            return "not found";
        } else {
            // 将筛选后的数据写入文件
            EasyExcel.write(inputFile, WhoCN.class)
                    .sheet("Filtered Data")
                    .doWrite(filteredData);
            return "found";
        }
    }


    /**
     * 将多个 CSV 文件打包到一个 ZIP 文件中
     *
     * @param csvFiles      CSV 文件路径列表
     * @param outputZipFile 输出的 ZIP 文件路径
     * @throws IOException 如果发生 I/O 错误
     */
    public static void packCsvFilesToZip(List<String> csvFiles, String outputZipFile) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(outputZipFile);
             ZipOutputStream zipOut = new ZipOutputStream(fos)) {

            for (String csvFile : csvFiles) {
                File fileToZip = new File(csvFile);
                if (!fileToZip.exists()) {
                    System.err.println("文件不存在: " + csvFile);
                    continue;
                }

                try (FileInputStream fis = new FileInputStream(fileToZip)) {
                    // 创建 ZIP 条目
                    ZipEntry zipEntry = new ZipEntry(fileToZip.getName());
                    zipOut.putNextEntry(zipEntry);

                    // 将文件内容写入 ZIP
                    byte[] bytes = new byte[1024];
                    int length;
                    while ((length = fis.read(bytes)) >= 0) {
                        zipOut.write(bytes, 0, length);
                    }
                }
            }
        }
    }

    public static String getCurrentTimeStr() {
        // 获取当前时间
        LocalDateTime currentTime = LocalDateTime.now();
        // 格式化时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        String formattedTime = currentTime.format(formatter);
        return formattedTime;
    }


    public static List<String> downloadFiles(String taskId, String fid, String fileType) {
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, SASOnlieConstant.REMOTE_SERVER_PROJECTID);
        String studyId = formInfo.get("studyId");
        String regex = "";
        String formInfoEdit="";
        //获取表单id
        String tableId = formInfo.get("tableId").toString();
        if (fileType.contains("xlsx")) {
            regex = "([^*]+)\\*([A-Z0-9]+\\.xlsx)\\|";
        } else if (fileType.contains("zip")) {
            regex = "([^*]+)\\*([A-Z0-9]+\\.zip)\\|";
        } else if (fileType.contains("csv")) {
            regex = "([^*]+)\\*([A-Z0-9]+\\.csv)\\|";
        }
        String param = formInfo.get("param");
        cn.hutool.json.JSONObject formInfoDTA= new cn.hutool.json.JSONObject(param);
        String id1= formInfoDTA.get("id").toString();
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "study_coding_plan");
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        if (com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size() > 0) {
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
             formInfoEdit = CDTMSAPI.getDataListInfo(token, tableId, "obj.studyid='" + studyInt + "'" + "and obj.id='" + id1 + "'", "edit", "");
        }

        //获取模板文件
         token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "studyinfo");
        String input =com.alibaba.fastjson.JSONArray.parseArray(formInfoEdit).getJSONObject(0).get(fid).toString();
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        String ufn = "";
        String fileName="";


        List<String> filePaths = new ArrayList<>();
        
        while (matcher.find()) {
            String originalName = matcher.group(1); // 获取原始文件名
            ufn = matcher.group(2);         // 获取UFN
            log.info("Original file name: {}", originalName);
            log.info("UFN: {}", ufn);
            
            String filePath = TEMP_FILE_PATH + originalName; // 使用原始文件名创建路径
            
            if (!ufn.isEmpty()) {
                try {
                    CDTMSAPI.downloadDataByUserSync(tableId, token, ufn, filePath);
                    filePaths.add(filePath);
                } catch (IOException e) {
                    log.error("Failed to download file: {}", ufn, e);
                    continue;
                }
            }
        }
        
        return filePaths;
    }


    public static List<String> downloadFilesByFN(String tableId,String fn, String fileType) {
        List<String> filePaths = new ArrayList<>();
        String regex = "";
        if (fileType.contains("xlsx")) {
            regex = "([^*]+)\\*([A-Z0-9]+\\.xlsx)\\|";
        } else if (fileType.contains("zip")) {
            regex = "([^*]+)\\*([A-Z0-9]+\\.zip)\\|";
        } else if (fileType.contains("csv")) {
            regex = "([^*]+)\\*([A-Z0-9]+\\.csv)\\|";
        }
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "studyinfo");
        String input =fn;
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        String ufn = "";
        while (matcher.find()) {
            String originalName = matcher.group(1); // 获取原始文件名
            ufn = matcher.group(2);         // 获取UFN
            log.info("Original file name: {}", originalName);
            log.info("UFN: {}", ufn);

            String filePath = TEMP_FILE_PATH + originalName; // 使用原始文件名创建路径

            if (!ufn.isEmpty()) {
                try {
                    CDTMSAPI.downloadDataByUserSync(tableId, token, ufn, filePath);
                    filePaths.add(filePath);
                } catch (IOException e) {
                    log.error("Failed to download file: {}", ufn, e);
                    continue;
                }
            }
        }
        return filePaths;
    }
}
