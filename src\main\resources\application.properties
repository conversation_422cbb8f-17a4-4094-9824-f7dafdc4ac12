server.port=8089
server.servlet.context-path=/medcoding_download
#preview-prfix=http://************:
preview-prfix=http://10.10.5.74:
#preview-prfix=http://10.10.5.139:

#\u591A\u6570\u636E\u6E90\u914D\u7F6E
spring.datasource.dynamic.primary=master
spring.datasource.dynamic.strict=false
#\u751F\u4EA7
spring.datasource.dynamic.datasource.master.url=************************************************************************************************************* = Asia/Shanghai
spring.datasource.dynamic.datasource.master.username=sastableau
spring.datasource.dynamic.datasource.master.password=hr123.com
#\u6D4B\u8BD5
#spring.datasource.dynamic.datasource.master.url=************************************************************************************************************* = Asia/Shanghai
#spring.datasource.dynamic.datasource.master.username=sastableau
#spring.datasource.dynamic.datasource.master.password=hr123.com

#\u9A8C\u8BC1
#spring.datasource.dynamic.datasource.master.url=**************************************************** e=true&characterEncoding=UTF-8&useSSL=true&serverTimezone = Asia/Shanghai
#spring.datasource.dynamic.datasource.master.username=sastableau
#spring.datasource.dynamic.datasource.master.password=hr123.com



#spring.datasource.dynamic.datasource.slave.url=**************************************
##spring.datasource.dynamic.datasource.slave.username=SYS
##spring.datasource.dynamic.datasource.slave.password=hengrui123#
##spring.datasource.dynamic.datasource.slave.username=scott
##spring.datasource.dynamic.datasource.slave.password=scott10.10.710.
#spring.datasource.dynamic.datasource.slave.username=gousy
#spring.datasource.dynamic.datasource.slave.password=hrgsy@cdtms


spring.datasource.dynamic.datasource.slave1.url=************************************************************************************************************ = Asia/Shanghai
spring.datasource.dynamic.datasource.slave1.username=root
#\u6D4B\u8BD5
#spring.datasource.dynamic.datasource.slave1.password=Hr@mysql1024
#\u751F\u4EA7
spring.datasource.dynamic.datasource.slave1.password=Hr@mysql1024




mybatis-plus.configuration.map-underscore-to-camel-case=false
mybatis-plus.configuration.auto-mapping-behavior=full
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.mapper-locations=classpath*:/mapper/*.xml

#xxl.job.admin.addresses=http://************:8088/xxl-job-admin
xxl.job.admin.addresses=http://***********:8088/xxl-job-admin
xxl.job.executor.address:
xxl.job.executor.appname:xxl-job-executor-scheduleMed
xxl.job.executor.ip:
xxl.job.executor.port:9998
xxl.job.executor.logpath:/data/xxl-job/jobhandler
xxl.job.executor.logretentiondays:-1
xxl.job.accessToken:default_token

