package com.hengrui.medcoding.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

@Component
@Mapper
@Repository
@DS("slave1")
public interface MedProBigFileMapper {

    //存储大文件的md5码
    int insertBigFileMd5(String fileName,String md5);

    //获取大文件的md5码
    String getBigFileMd5(String md5);
}
