package com.hengrui.medcoding;

import com.hengrui.medcoding.service.DownloadMedcoding;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

import java.util.Arrays;

@SpringBootApplication
@MapperScan(basePackages = "com.hengrui.medcoding.mapper")
@Slf4j


public class MedcodingDownloadApplication  implements CommandLineRunner{


    public static void main(String[] args) {
        // 这里可以访问到命令行参数
        log.info("获取到的命令行参数是:" + Arrays.toString(args));
        SpringApplication.run(MedcodingDownloadApplication.class, args);
    }

    @Autowired
    DownloadMedcoding downloadMedcoding;



    @Override
    public void run(String... args) throws Exception {
        downloadMedcoding.downloadAllCodingFile(args);
    }



//    public static void main(String[] args) {
//        SpringApplication.run(MedcodingDownloadApplication.class, args);
//    }
}
