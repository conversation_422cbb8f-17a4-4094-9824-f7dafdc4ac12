<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengrui.medcoding.mapper.MedProBigFileMapper">
    <insert id="insertBigFileMd5" parameterType="string">
        insert into medcoding_big_file_md5 (file_name,md5_value,create_time)
        values(#{fileName},#{md5},now());
    </insert>

    <select id="getBigFileMd5" parameterType="string" resultType="string">
        select md5_value from medcoding_big_file_md5 where md5_value=#{md5}
    </select>
</mapper>