package com.hengrui.medcoding.jobhandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hengrui.medcoding.constant.SASOnlieConstant;
import com.hengrui.medcoding.service.DownloadMedcoding;
import com.hengrui.medcoding.utils.CDTMSAPI;
import com.hengrui.medcoding.utils.FilesUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * @ClassName AutoScheduleCDTMS
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/7/10 9:10
 * @Version 1.0
 **/

@Component
@Slf4j
public class AutoScheduleCDTMS {

    @Autowired
    DownloadMedcoding downloadMedcoding;


    @XxlJob("scheduleMedCoding")
    @Transactional
    public void scheduleCDTMSHandler() throws Exception {
        AutoScheduleCDTMS.log.info("--------------------------------调度任务已经启动-----------------");
        //获取token
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "coding");
        //获取所有studyid
        String studyIdNum = CDTMSAPI.getDataListInfoWithPage(token, "Xsht", "obj.active=1", "edit", "",1000);
        String studyInfo = CDTMSAPI.getDataListInfoWithPage(token, "xsht", "obj.active=1", "", "",1000);


        //  String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
        JSONArray objects = JSON.parseArray(studyIdNum);
        JSONArray StudyObjects = JSON.parseArray(studyInfo);
        for(int i=0;i<objects.size();i++){
            String studyInt=objects.getJSONObject(i).get("id").toString();
            String studyid=StudyObjects.getJSONObject(i).get("studyid").toString();
            String formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, token);
            //创建新记录
            JSONObject param =new JSONObject();
            param.put("studyid",studyInt);
            param.put("zq", 6);
            String saveRecord = CDTMSAPI.usersyndataSave(token, "coding", formId, "", "", param.toString());
            String dataId=JSON.parseObject(saveRecord).get("id").toString();
            //调用医学编码下载接口，回填到相应的记录上
            downloadMedcoding.downloadCodingFileBystudyId3(studyid,token,dataId);
        }






    }


}



