<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengrui.medcoding.mapper.MedcodingMapper">
    <select id="getWhoDrugBatchInfo" resultType="map" parameterType="string">
        SELECT
            id,
            COL_VER,
            COL_SN
        FROM
            tbl_whoddimport
        WHERE
                ( id, COL_CREATETIME ) IN (
                SELECT
                    MAX( id ),
                    MAX( COL_CREATETIME )
                FROM
                    tbl_whoddimport
                WHERE
                    COL_STUDYID = #{studyId}
                GROUP BY
                    COL_FILDESC
            )
    </select>
    <select id="getMedDrugBatchInfo" resultType="map" parameterType="string">
        SELECT
            id,
            COL_VER,
            COL_SN
        FROM
            tbl_meddraimport
        WHERE
                ( id, COL_CREATETIME ) IN (
                SELECT
                    MAX( id ),
                    MAX( COL_CREATETIME )
                FROM
                    tbl_meddraimport
                WHERE
                    COL_STUDYID = #{studyId}
                GROUP BY
                    COL_FILDESC
            )
    </select>
    <select id="getAllStudyIds" resultType="string">
        SELECT DISTINCT  COL_STUDYID FROM tbl_study
    </select>
</mapper>