package com.hengrui.medcoding.config;

import com.hengrui.medcoding.constant.SASOnlieConstant;
import io.minio.MinioClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Minio 配置信息
 *
 * <AUTHOR>
 */
@Configuration
public class MinioConfig {
    private final MinioClient minioClient = initMinioClient();

    /**
     * 服务地址
     */
    private String url;

    /**
     * 用户名
     */
    private String accessKey;

    /**
     * 密码
     */
    private String secretKey;

    /**
     * 存储桶名称
     */
    private String bucketName;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getBucketName() {
        return bucketName;
    }

    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }


    public MinioClient initMinioClient() {
        return MinioClient.builder().endpoint(SASOnlieConstant.MINIO_URL).credentials("minioadmin", "minioadmin").build();
    }

    @Bean
    public MinioClient getMinioClient() {
        return this.minioClient;
    }

}

