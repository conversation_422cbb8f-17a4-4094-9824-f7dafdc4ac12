package com.hengrui.medcoding.service;

import java.util.Map;

public interface MedCodingService {
    Map<String, String> getMedCodingFile(String taskId, String projectId, String server);

    Map<String, String> putMedCodingFile(String taskId, String projectId, String server);

   String getMedCodingFiles(String taskId, String projectId, String server);

    String getMedCodingUTR(String taskId, String projectId,String server);
}
