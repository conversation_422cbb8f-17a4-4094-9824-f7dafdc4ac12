<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hengrui.medcoding.mapper.MedcodingMapper">
    <select id="getWhoDrugBatchInfo" resultType="map" parameterType="string">
        WITH temp AS (
            SELECT
                id,
                COL_STUDYID,
                COL_VER,
                COL_SN
            FROM
                (
                    SELECT
                        MAX( id ) AS id,
                        MAX( COL_CREATETIME ) AS COL_CREATETIME,
                        REGEXP_REPLACE(
                                replace(
                                        replace(
                                                replace(any_value(COL_FILDESC), ' ', ''),
                                            CHAR(10), ''
                                            ),
                                    CHAR(13), ''
                                    ),
                                '\\[.*$', ''
                            ) as COL_FILDESC,
                        MAX( COL_STUDYID ) AS COL_STUDYID,
                        MAX( COL_VER ) AS COL_VER,
                        MAX( COL_SN ) AS COL_SN
                    FROM
                        tbl_whoddimport
                    WHERE
                        id is not null
                      AND
                        COL_STUDYID = #{studyId}
                      AND COL_FILDESC NOT LIKE '%,%'
                      AND IFNULL( COL_COMMENT, '' ) NOT LIKE '%错%'
                      and COL_CREATETIME>='2023-06-01 00:00:00'
                    GROUP BY
                        COL_FILDESC
                ) result
            GROUP BY
                COL_FILDESC
        ) SELECT
              any_value ( temp.id ) AS id,
              any_value ( temp.COL_VER ) AS COL_VER,
              any_value ( temp.COL_SN ) AS COL_SN,
              date_format( MAX( tbl_whoddimportdetail.COL_LASTMODIFYTIME ), '%Y-%m-%d %H:%i:%s' ) AS exportTime
        FROM
            temp
                LEFT JOIN tbl_whoddimportdetail ON ( temp.COL_STUDYID = tbl_whoddimportdetail.COL_STUDYID AND temp.COL_SN = tbl_whoddimportdetail.COL_DSN )
        GROUP BY
            tbl_whoddimportdetail.COL_DSN
    </select>
    <select id="getMedDrugBatchInfo" resultType="map" parameterType="string">
        WITH temp AS (
            SELECT
                id,
                COL_STUDYID,
                COL_VER,
                COL_SN
            FROM
                (
                    SELECT
                        MAX( id ) AS id,
                        MAX( COL_CREATETIME ) AS COL_CREATETIME,
                        REGEXP_REPLACE(
                                replace(
                                        replace(
                                                replace(any_value(COL_FILDESC), ' ', ''),
                                            CHAR(10), ''
                                            ),
                                    CHAR(13), ''
                                    ),
                                '\\[.*$', ''
                            ) as COL_FILDESC,
                        MAX( COL_STUDYID ) AS COL_STUDYID,
                        MAX( COL_VER ) AS COL_VER,
                        MAX( COL_SN ) AS COL_SN
                    FROM
                        tbl_meddraimport
                    WHERE
                        id is not null
                      AND
                        COL_STUDYID = #{studyId}
                      AND COL_FILDESC NOT LIKE '%,%'
                      AND IFNULL( COL_COMMENT, '' ) NOT LIKE '%错%'
                     and COL_CREATETIME>='2023-06-01 00:00:00'
                    GROUP BY
                        COL_FILDESC
                ) result
            GROUP BY
                COL_FILDESC
        ) SELECT
              any_value ( temp.id ) AS id,
              any_value(temp.COL_STUDYID) as COL_STUDYID,
              any_value ( temp.COL_VER ) AS COL_VER,
              any_value ( temp.COL_SN ) AS COL_SN,
              date_format( MAX( tbl_meddraimportdetail.COL_LASTMODIFYTIME ), '%Y-%m-%d %H:%i:%s' ) AS exportTime
        FROM
            temp
                LEFT JOIN tbl_meddraimportdetail ON ( temp.COL_STUDYID = tbl_meddraimportdetail.COL_STUDYID AND temp.COL_SN = tbl_meddraimportdetail.COL_DSN )
        GROUP BY
            tbl_meddraimportdetail.COL_DSN
    </select>
    <select id="getAllStudyIds" resultType="string">
        SELECT DISTINCT  COL_STUDYID FROM tbl_study limit 50
    </select>
    <select id="getMedProjectLangType" resultType="string" parameterType="string">
        SELECT
            COL_LANGUAGE
        FROM
            tbl_meddraimport
        WHERE
            COL_STUDYID = #{studyId} and  IFNULL( COL_COMMENT, '' ) NOT LIKE '%错%'
        limit 1
    </select>

    <select id="getWhoProjectLangType" resultType="string" parameterType="string">
        SELECT
            COL_LANGUAGE
        FROM
            tbl_whoddimport
        WHERE
            COL_STUDYID = #{studyId} and  IFNULL( COL_COMMENT, '' ) NOT LIKE '%错%'
            limit 1
    </select>
</mapper>