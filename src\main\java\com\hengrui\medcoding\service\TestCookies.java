//package com.hengrui.medcoding.service;
//
//import java.io.IOException;
//import java.net.URL;
//import org.jsoup.Connection;
//import org.jsoup.Jsoup;
//import org.jsoup.nodes.Document;
//
//public class TestCookies {
//
//    public static void main(String[] args) {
//        try {
//            URL url = new URL("https://clinical.hengruipharma.com/login/login.do?time=1687226019136&loginId=hui.zhou.hz36%40hengrui.com&password=28e970734c3562855ace98b3acb37f12&code=bado&base64Code=X3OQLm5425s%3D");
//            Connection conn = Jsoup.connect(url.toString());
//            conn.header("Content-Type", "application/json;charset=UTF-8");
//            conn.header("L", "zh_CN");
//            conn.header("Token","");
//            conn.header("Accept", "application/json, text/plain, */*");
//            conn.ignoreContentType(true).ignoreHttpErrors(true);
//            conn.header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
//            Document doc = conn.method(Connection.Method.GET).execute().parse();
//            String requestHeaders = conn.request().headers().toString();
//            System.out.println(doc.toString());
//            System.out.println(requestHeaders);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }
//}