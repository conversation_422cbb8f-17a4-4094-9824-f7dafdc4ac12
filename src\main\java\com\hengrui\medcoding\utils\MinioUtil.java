package com.hengrui.medcoding.utils;

import com.hengrui.medcoding.config.MinioConfig;
import com.hengrui.medcoding.mapper.MedProBigFileMapper;
import io.minio.PutObjectArgs;
import io.minio.SetObjectTagsArgs;
import io.minio.StatObjectArgs;
import io.minio.StatObjectResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class MinioUtil {
    @Autowired
    private MinioConfig minioConfig;

    @Autowired
    MedProBigFileMapper medProBigFileMapper;


    public boolean isObjectExist(String objectName, MinioConfig minioConfig) {
        boolean exist = true;
        try {
            StatObjectResponse stat = minioConfig.getMinioClient().statObject(
                    StatObjectArgs.builder().bucket(minioConfig.getBucketName()).object(objectName).build());
        } catch (Exception e) {
            exist = false;
        }
        return exist;
    }


    /**
     * 本地文件上传接口
     *
     * @param file 上传的文件
     * @return 访问地址
     * @throws Exception
     */
    public String uploadFile(MultipartFile file, String fileMd5, String studyId, String exportTime,String importId,String timeViaFileNameprefix,String timeViaFileNamesufix) throws Exception {

        String objectName = "/medcoding/" + file.getName();
        minioConfig.setBucketName("doc");
        //标签定义
        Map<String, String> map = new HashMap<>();
        map.put("key1", "medCoding");
        map.put("key2", studyId);
        map.put("key3", exportTime);
        map.put("key4",importId);
        map.put("key5",timeViaFileNameprefix);
        map.put("key6",timeViaFileNamesufix);
        // 1. 获取object的metadata
        if (isObjectExist(objectName, minioConfig)) {
            //存在同名文件
            StatObjectResponse stat = minioConfig.getMinioClient().statObject(
                    StatObjectArgs.builder().bucket(minioConfig.getBucketName()).object(objectName).build());
            //2.比较minio上的etag和本地文件的md5是否一致
            MinioUtil.log.info("本地文件的MD5码为:" + fileMd5 + "\n" + "minio上的MD5码为:" + stat.etag());
            //判断是否为大文件
            Boolean isBigFile = false;
            String bigFileMd5 = "";
            if (stat.etag().split("-").length > 1) {
                //是大文件
                isBigFile = true;
                //判断是否存在相同文件
                bigFileMd5 = medProBigFileMapper.getBigFileMd5(fileMd5);
                if (ObjectUtils.isEmpty(bigFileMd5)) {
                    //新增该条记录
                    medProBigFileMapper.insertBigFileMd5(file.getName(), fileMd5);
                }
            }
            //非大文件直接比较md5
            if (!stat.etag().equals(fileMd5) && !isBigFile) {
                PutObjectArgs args = PutObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .stream(file.getInputStream(), file.getInputStream().available(), -1)
                        .contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                        .build();
                minioConfig.getMinioClient().putObject(args);
                //加标签
                SetObjectTagsArgs setObjectTagsArgs = SetObjectTagsArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .tags(map)
                        .build();
                minioConfig.getMinioClient().setObjectTags(setObjectTagsArgs);
                log.info("文件：" + file.getName() + "已经上传至minio");
            } else if (!stat.etag().equals(fileMd5) && isBigFile && ObjectUtils.isEmpty(bigFileMd5)) {
                //MD5码不同，且是大文件，且没有记录过MD5码
                PutObjectArgs args = PutObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .stream(file.getInputStream(), file.getInputStream().available(), -1)
                        .contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                        .build();
                minioConfig.getMinioClient().putObject(args);
                //加标签
                SetObjectTagsArgs setObjectTagsArgs = SetObjectTagsArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .tags(map)
                        .build();
                minioConfig.getMinioClient().setObjectTags(setObjectTagsArgs);
                log.info("大文件：" + file.getName() + "已经上传至minio");
            } else {
                log.info("文件：" + file.getName() + "在minio上已经存在相同文件！");
            }

        } else {
            log.info("文件：" + file.getName() + "在minio上没有相同文件，直接上传");
            //直接上传
            PutObjectArgs args = PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .stream(file.getInputStream(), file.getInputStream().available(), -1)
                    .contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                    .build();
            minioConfig.getMinioClient().putObject(args);
            //加标签
            SetObjectTagsArgs setObjectTagsArgs = SetObjectTagsArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .tags(map)
                    .build();
            minioConfig.getMinioClient().setObjectTags(setObjectTagsArgs);
            log.info("文件：" + file.getName() + "已经上传至minio");
        }
        return minioConfig.getUrl() + "/" + minioConfig.getBucketName() + "/" + file.getName();
    }




    public String uploadFileNoJudge(MultipartFile file, String fileMd5, String studyId, String exportTime,String importId,String timeViaFileNameprefix,String timeViaFileNamesufix) throws Exception {

        String objectName = "/medcoding/" + file.getName();
        minioConfig.setBucketName("doc");
        //标签定义
        Map<String, String> map = new HashMap<>();
        map.put("key1", "medCoding");
        map.put("key2", studyId);
        map.put("key3", exportTime);
        map.put("key4",importId);
        map.put("key5",timeViaFileNameprefix);
        map.put("key6",timeViaFileNamesufix);
        // 1. 获取object的metadata
        if (isObjectExist(objectName, minioConfig)) {
            //存在同名文件
            StatObjectResponse stat = minioConfig.getMinioClient().statObject(
                    StatObjectArgs.builder().bucket(minioConfig.getBucketName()).object(objectName).build());
            //2.比较minio上的etag和本地文件的md5是否一致
            MinioUtil.log.info("本地文件的MD5码为:" + fileMd5 + "\n" + "minio上的MD5码为:" + stat.etag());
            //判断是否为大文件
            Boolean isBigFile = false;
            String bigFileMd5 = "";
            if (stat.etag().split("-").length > 1) {
                //是大文件
                isBigFile = true;
                //判断是否存在相同文件
                bigFileMd5 = medProBigFileMapper.getBigFileMd5(fileMd5);
                if (ObjectUtils.isEmpty(bigFileMd5)) {
                    //新增该条记录
                    medProBigFileMapper.insertBigFileMd5(file.getName(), fileMd5);
                }
            }
            //非大文件直接比较md5
            if (!isBigFile) {
                PutObjectArgs args = PutObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .stream(file.getInputStream(), file.getInputStream().available(), -1)
                        .contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                        .build();
                minioConfig.getMinioClient().putObject(args);
                //加标签
                SetObjectTagsArgs setObjectTagsArgs = SetObjectTagsArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .tags(map)
                        .build();
                minioConfig.getMinioClient().setObjectTags(setObjectTagsArgs);
                log.info("文件：" + file.getName() + "已经上传至minio");
            } else if ( isBigFile ) {
                //MD5码不同，且是大文件，且没有记录过MD5码
                PutObjectArgs args = PutObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .stream(file.getInputStream(), file.getInputStream().available(), -1)
                        .contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                        .build();
                minioConfig.getMinioClient().putObject(args);
                //加标签
                SetObjectTagsArgs setObjectTagsArgs = SetObjectTagsArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(objectName)
                        .tags(map)
                        .build();
                minioConfig.getMinioClient().setObjectTags(setObjectTagsArgs);
                log.info("大文件：" + file.getName() + "已经上传至minio");
            } else {
                log.info("文件：" + file.getName() + "在minio上已经存在相同文件！");
            }

        } else {
            log.info("文件：" + file.getName() + "在minio上没有相同文件，直接上传");
            //直接上传
            PutObjectArgs args = PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .stream(file.getInputStream(), file.getInputStream().available(), -1)
                    .contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                    .build();
            minioConfig.getMinioClient().putObject(args);
            //加标签
            SetObjectTagsArgs setObjectTagsArgs = SetObjectTagsArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(objectName)
                    .tags(map)
                    .build();
            minioConfig.getMinioClient().setObjectTags(setObjectTagsArgs);
            log.info("文件：" + file.getName() + "已经上传至minio");
        }
        return minioConfig.getUrl() + "/" + minioConfig.getBucketName() + "/" + file.getName();
    }
}
