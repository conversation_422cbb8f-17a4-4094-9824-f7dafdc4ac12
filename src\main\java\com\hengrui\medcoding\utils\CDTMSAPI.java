package com.hengrui.medcoding.utils;


import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.hengrui.medcoding.constant.SASOnlieConstant;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.security.KeyManagementException;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * @ClassName CDTMSAPI
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/22 13:25
 * @Version 1.0
 **/
@Slf4j
@Component
public class CDTMSAPI {


    @Autowired
    Decode64Util decode64Util;

    @Autowired
    MinioUtil minioUtil;

    private static void extractZipFile(String zipFilePath) throws IOException {
        Path destinationDir = Paths.get(zipFilePath).getParent();
        if (destinationDir == null) {
            destinationDir = Paths.get(".");
        }

        log.info("解压缩 zip 文件到: " + destinationDir);

        try (ZipInputStream zipIn = new ZipInputStream(new FileInputStream(zipFilePath))) {
            ZipEntry entry = zipIn.getNextEntry();

            while (entry != null) {
                Path filePath = destinationDir.resolve(entry.getName());

                // Create directories if needed
                if (entry.isDirectory()) {
                    Files.createDirectories(filePath);
                } else {
                    // Create parent directories if needed
                    Files.createDirectories(filePath.getParent());

                    // Extract file
                    try (BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(filePath.toFile()))) {
                        byte[] buffer = new byte[4096];
                        int read;
                        while ((read = zipIn.read(buffer)) != -1) {
                            bos.write(buffer, 0, read);
                        }
                    }
                    log.info("解压缩: " + entry.getName());
                }

                zipIn.closeEntry();
                entry = zipIn.getNextEntry();
            }
        }

        log.info("Zip 完成解压缩!");
    }
    @SneakyThrows
    public static void downloadDataByUserSync(String tableId, String token, String ufn, String filePath) throws IOException {
        // Call the download interface
        String fileUrl = SASOnlieConstant.REMOTE_SERVER_API + "usersyn/download?tableid=" + tableId + "&token=" + URLEncoder.encode(token, "UTF-8") + "&ufn=" + URLEncoder.encode(ufn, "UTF-8");
        log.info("----------------文件下载的url是："+fileUrl+"----------------------------------------------");
        // Set up to access the https request through ip address
        HttpsURLConnection.setDefaultHostnameVerifier(new NullHostNameVerifier());
        TrustManager[] tm = {new MyX509TrustManager()};
        SSLContext sslContext = SSLContext.getInstance("TLS");
        try {
            sslContext.init(null, tm, new java.security.SecureRandom());
        } catch (KeyManagementException e) {
            throw new RuntimeException(e);
        }
        // Get the SSLSocketFactory object from the above SSLContext object
        SSLSocketFactory ssf = sslContext.getSocketFactory();
        String urlStr = fileUrl;
        URL url = null;
        try {
            url = new URL(urlStr);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }
        HttpsURLConnection con = null;
        try {
            con = (HttpsURLConnection) url.openConnection();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        con.setSSLSocketFactory(ssf);
        try {
            con.setRequestMethod("GET"); // Set to submit data using POST method
        } catch (ProtocolException e) {
            throw new RuntimeException(e);
        }
        con.setDoInput(true); // Open input stream to get data from the server
        con.setDoOutput(true);// Open output stream to send data to the server
        // Set the sending parameters
        PrintWriter out = null;
        try {
            out = new PrintWriter(new OutputStreamWriter(con.getOutputStream(), "UTF-8"));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        out.flush();
        out.close();
        try {
            InputStream in = con.getInputStream();
            Files.copy(in, Paths.get(filePath), StandardCopyOption.REPLACE_EXISTING);
            // Extract the zip file to current directory
            if(filePath.contains(".zip")){
                extractZipFile(filePath);
            }

        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    public static String notify(String taskId) {
        log.info("the request taskId is:" + taskId);
        String responseStr = "";
        String requestURL = SASOnlieConstant.REMOTE_SERVER_API + "remoteButtonTask/notify?taskId=" + taskId + "&projectId=" + BlindConstant.TABLE_ID;
        try {
            URL url = new URL(requestURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            log.info("Response Code: " + responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                JSONObject jsonResponse = new JSONObject(response.toString());
                responseStr = response.toString();
                // Extract the study_id
                log.info("response is : " + responseStr);
                return responseStr;
            } else {
                log.info("GET request not worked");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "fail";
    }


    public String uploadSASOutputFile(String taskId, String formId, String filePath) {
        String boundary = Long.toHexString(System.currentTimeMillis()); // Random boundary for multipart
        String CRLF = "\r\n"; // Line separator required by multipart/form-data.
        File file = new File(filePath);
        String fileName = file.getName();
        String urlString = SASOnlieConstant.REMOTE_SERVER_API + "remoteButtonTask/upload"
                + "?taskId=" + taskId + "&formId=" + formId + "&fid=" + fileName + "&fn=" + fileName + "&projectId=" + BlindConstant.TABLE_ID;
        try {
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setDoOutput(true);
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);

            try (OutputStream output = connection.getOutputStream();
                 PrintWriter writer = new PrintWriter(new OutputStreamWriter(output, "UTF-8"), true);
                 FileInputStream fileInputStream = new FileInputStream(file)) {
                // Send file data.
                writer.append("--" + boundary).append(CRLF);
                writer.append("Content-Disposition: form-data; name=\"file\"; filename=\"" + fileName + "\"").append(CRLF);
                writer.append("Content-Type: application/octet-stream").append(CRLF); // Change accordingly
                writer.append(CRLF).flush();
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                    output.write(buffer, 0, bytesRead);
                }
                output.flush(); // Important! Output cannot be closed. Close of writer will close output as well.
                writer.append(CRLF).flush(); // CRLF is important! It indicates end of boundary.
                // End of multipart/form-data.
                writer.append("--" + boundary + "--").append(CRLF).flush();
            }

            // Request is lazily fired whenever you need to obtain information about response.
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (InputStream responseStream = connection.getInputStream();
                     BufferedReader reader = new BufferedReader(new InputStreamReader(responseStream))) {
                    String line;
                    StringBuilder response = new StringBuilder();
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    log.info("Response: " + response.toString());
                }
                //更新
                String notify = notify(taskId);
                if (!notify.equals("fail")) {
                    return "success";
                }

            } else {
                log.info("Server returned non-OK status: " + responseCode);
            }
        } catch (MalformedURLException e) {
            log.info("URL was malformed.");
        } catch (IOException e) {
            log.info("An I/O error occurred: " + e.getMessage());
        }
        return "fail";
    }


    public static String getFormIdByTaskId_back(String taskId, String projectId) {
        boolean validJson = isValidJson(projectId);
        log.info("the request taskId is:" + taskId);
        String formId = "";
        if (!validJson) {
            String requestURL = SASOnlieConstant.REMOTE_SERVER_API + "remoteButtonTask/formId?taskId=" + taskId + "&projectId=" + projectId;
            try {
                URL url = new URL(requestURL);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                int responseCode = connection.getResponseCode();
                log.info("Response Code: " + responseCode);
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    String inputLine;
                    StringBuilder response = new StringBuilder();
                    while ((inputLine = in.readLine()) != null) {
                        response.append(inputLine);
                    }
                    in.close();
                    // Convert response to JSONObject for parsing
                    JSONObject jsonResponse = new JSONObject(response.toString());
                    // Extract the study_id
                    if (!ObjectUtils.isEmpty(jsonResponse.getJSONObject("data"))) {
                        formId = jsonResponse.getJSONObject("data").getStr("formId");
                    }
                    log.info("formId: " + formId);
                    return formId;
                } else {
                    log.info("GET request not worked");
                    return "falil";
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return formId;
    }

    //定时调度，获取任务信息
    public static String getFormIdByTaskId(String paramA, String paramB) {
        String formId = getFormIdByTaskId_back(paramA, paramB);
        if (ObjectUtils.isEmpty(formId) || formId.isEmpty()) {
            //没有查到formId
            String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "crf_handover");
            formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, token);
        }
        return formId;
    }





    public static String getUrlPrefix(String sysType) {
        Map<String, String> prefixMap = new HashMap<>();
        prefixMap.put("cdtms-pro", "cdtms.hengrui.com");
        prefixMap.put("cdtms-test", "cdtms-tst.hengrui.com");
        prefixMap.put("cdtms-val", "meduap-tst.hengrui.com");
        prefixMap.put("edc-pro", "clinical.hengruipharma.com:1818");
        prefixMap.put("edc-test", "cdtms-tst.hengrui.com:82");
        return prefixMap.getOrDefault(sysType, "Invalid Param"); // Return "Invalid Param" if no match found
    }

    //支持自动调度的创建表单记录，并获取相应的表单信息
    public static Map<String, String> getFormInfoByTaskId(String paramA, String paramB) {
        Map<String, String> formInfo = getFormInfoByTaskId_back(paramA, paramB);
        CDTMSAPI.log.info("formInfo: " + formInfo);
        //如果差不到表单信息，就执行自动调度创建记录
        if (ObjectUtils.isEmpty(formInfo.get("param"))) {
            CDTMSAPI.log.info("---------------------------paramJson is : " + paramB);
            com.alibaba.fastjson.JSONObject paramJson = com.alibaba.fastjson.JSONObject.parseObject(paramB);
            String tableId = paramJson.get("tableId").toString();
            String studyid = paramJson.get("studyid").toString();
            com.alibaba.fastjson.JSONObject formData = paramJson.getJSONObject("formData");
            //根据userSync接口查询表单信息
            //1.通过paramA<-->token, paramB<--->studyId   调用getformid
            String formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, paramA);
            //2.根据表单接口，查询对应的项目的studyId 整数值
            String studyIdNum = CDTMSAPI.getDataListInfo(paramA, "Xsht", "obj.studyid='" + studyid + "'", "edit", "");
            String studyId = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            //3.先判断formData是否存在记录id
            formData.put("studyid", studyId);
            String id1 = "";
            if (!formData.containsKey("id")) {
                //创建一条表单记录
                String recordId = CDTMSAPI.usersyndataSave(paramA, tableId, formId, "", "", formData.toString());
                com.alibaba.fastjson.JSONObject saveRestult = com.alibaba.fastjson.JSONObject.parseObject(recordId);
                //4.获取记录ID
                id1 = saveRestult.get("id").toString();
            } else {
                id1 = formData.get("id").toString();
            }

            //5.获取新建记录的表单信息
            String param = CDTMSAPI.getDataListInfo(paramA, tableId, "obj.studyid='" + studyId + "'" + "and obj.id='" + id1 + "'", "edit", "");
            //6.获取新增的表单信息
            String addInfo = com.alibaba.fastjson.JSONArray.parseArray(param).getJSONObject(0).toString();
            formInfo.put("studyId", studyid);
            formInfo.put("param", addInfo);
            formInfo.put("recordId", id1);
            formInfo.put("tableId", tableId);
        }
        return formInfo;
    }

    //getFormInfo By taskId and projectId
    public static Map<String, String> getFormInfoByTaskId_back(String taskId, String projectId) {
        log.info("the request taskId is:" + taskId + ",and projectId is :" + projectId);
        boolean validJson = isValidJson(projectId);
        Map<String, String> formInfoMap = new HashMap<>();
        String studyId = "";
        String param = "";
        String dataVersion = "";
        String tableId = "";
        if (!validJson) {
            String requestURL = SASOnlieConstant.REMOTE_SERVER_API + "remoteButtonTask/getInfo?taskId=" + taskId + "&projectId=" + projectId;
            try {
                URL url = new URL(requestURL);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                int responseCode = connection.getResponseCode();
                log.info("Response Code: " + responseCode);
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    String inputLine;
                    StringBuilder response = new StringBuilder();
                    while ((inputLine = in.readLine()) != null) {
                        response.append(inputLine);
                    }
                    in.close();
                    // Convert response to JSONObject for parsing
                    JSONObject jsonResponse = new JSONObject(response.toString());
                    // Extract the study_id
                    if (!ObjectUtils.isEmpty(jsonResponse.getJSONObject("data"))) {
                        studyId = jsonResponse.getJSONObject("data").getJSONObject("data").getStr("study_id");
                        if(ObjectUtils.isEmpty(studyId)|| studyId.equals("null")||studyId.isEmpty()){
                            studyId = jsonResponse.getJSONObject("data").getJSONObject("data").getStr("studyid");
                        }
                        dataVersion = jsonResponse.getJSONObject("data").getJSONObject("data").getStr("protocol_v");
                        param = jsonResponse.getJSONObject("data").getJSONObject("data").toString();
                        tableId = jsonResponse.getJSONObject("data").get("tid").toString();
                        log.info("param to String is: " + param);
                        log.info("studyId is: " + studyId);

                    }


                } else {
                    log.info("GET request not worked");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        formInfoMap.put("studyId", studyId);
        formInfoMap.put("dataVersion", dataVersion);
        formInfoMap.put("param", param);
        formInfoMap.put("tableId", tableId);
        return formInfoMap;
    }


    /**
     * This method retrieves information from the UAP API based on the provided token, tableId, and filtersParam.
     *
     * @param token        The token for authentication
     * @param tableId      The table ID for the request
     * @param filtersParam The filters to be applied to the request
     * @return The data retrieved from the API
     */
    public static String getDataListInfo(String token, String tableId, String filtersParam, String type, String orderColumn) {
        // Log the request parameters
        try {
            filtersParam = URLEncoder.encode(filtersParam, "UTF-8");
//             filtersParam = Base64.getEncoder().encodeToString(filtersParam.getBytes());
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        log.info("the request token is:" + token + ",and tableId is :" + tableId + ",and filtersParam is :" + filtersParam);
        String data = "";
        String requestURL = "";
        if (!type.isEmpty()) {
            requestURL = SASOnlieConstant.REMOTE_SERVER_API + "usersyn/datalist?token=" + token + "&tableid=" + tableId
                    + "&type=" + type
                    + "&where=" + filtersParam;
        } else if (!orderColumn.isEmpty()) {
            requestURL = SASOnlieConstant.REMOTE_SERVER_API + "usersyn/datalist?token=" + token + "&tableid=" + tableId
                    + "&where=" + filtersParam + "&orderby=" + orderColumn;
        } else {
            requestURL = SASOnlieConstant.REMOTE_SERVER_API + "usersyn/datalist?token=" + token + "&tableid=" + tableId
                    + "&where=" + filtersParam;
        }

        try {
            URL url = new URL(requestURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            log.info("Response Code: " + responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                JSONArray jsonResponse = new JSONArray(response.toString());
                // Extract the data field
                data = jsonResponse.toString();
                log.info("data: " + data);
                return data;
            } else {
                log.info("GET request not worked");
                return "fail";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }


    public static String getDataListInfoWithPage(String token, String tableId, String filtersParam, String type, String orderColumn,int pageSize) {
        // Log the request parameters
        try {
            filtersParam = URLEncoder.encode(filtersParam, "UTF-8");
//             filtersParam = Base64.getEncoder().encodeToString(filtersParam.getBytes());
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        log.info("the request token is:" + token + ",and tableId is :" + tableId + ",and filtersParam is :" + filtersParam);
        String data = "";
        String requestURL = "";
        if (!type.isEmpty()) {
            requestURL = SASOnlieConstant.REMOTE_SERVER_API + "usersyn/datalist?token=" + token + "&tableid=" + tableId
                    + "&type=" + type
                    + "&where=" + filtersParam;
        } else if (!orderColumn.isEmpty()) {
            requestURL = SASOnlieConstant.REMOTE_SERVER_API + "usersyn/datalist?token=" + token + "&tableid=" + tableId
                    + "&where=" + filtersParam + "&orderby=" + orderColumn;
        }else if(pageSize>0){
            requestURL = SASOnlieConstant.REMOTE_SERVER_API + "usersyn/datalist?token=" + token + "&tableid=" + tableId
                    + "&where=" + filtersParam + "&pagesize=" + pageSize+"&pagecount=1";
        } else {
            requestURL = SASOnlieConstant.REMOTE_SERVER_API + "usersyn/datalist?token=" + token + "&tableid=" + tableId
                    + "&where=" + filtersParam;
        }

        try {
            URL url = new URL(requestURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            log.info("Response Code: " + responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                JSONArray jsonResponse = new JSONArray(response.toString());
                // Extract the data field
                data = jsonResponse.toString();
                log.info("data: " + data);
                return data;
            } else {
                log.info("GET request not worked");
                return "fail";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }


    //get token by projectid and secret
    public static String getToken(String requestPrefix, String projectid, String secret, String tableId) {
        log.info("the request projectid is:" + projectid + ",and tableId is :" + tableId + ",and secret is :" + secret);
        String token = "";
        String requestURL = requestPrefix + "usersyn/gettoken?projectid=" + projectid + "&secret=" + secret
                + "&tableid=" + tableId;
        try {
            URL url = new URL(requestURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            log.info("Response Code: " + responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                JSONObject jsonResponse = new JSONObject(response.toString());
                // Extract the study_id
                token = jsonResponse.get("token").toString();
                log.info("token: " + token);
                return token;
            } else {
                log.info("GET request not worked");
                return "falil";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return token;
    }


    public static void dataSave(JSONObject param) {
        String token = "";
        String taskId = param.get("taskId").toString();
        String formId = param.get("formId").toString();
        String paramData = param.get("data").toString();
        //2.call getSubjectsInfo API
        CDTMSAPI.log.info("The request taskId is: " + param.get("taskId") + " and formId is: " + formId + " and projectId : " + param.get("projectId") + "and dataId is :" + paramData);
        //判断是否是手动触发
        if (!isValidJson(param.get("projectId").toString())) {
            String requestURL = SASOnlieConstant.REMOTE_SERVER_API + "remoteButtonTask/dataSave?taskId=" + param.get("taskId") + "&formId=" + param.get("formId") + "&projectId=" + param.get("projectId");
            CDTMSAPI.log.info("---------------------------------------the dataSave request URL is :" + requestURL);
            try {
                URL url = new URL(requestURL);
                HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
                connection.setDoOutput(true); // Set this before setting the request method
                connection.setRequestMethod("POST");
                connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
                connection.setRequestProperty("Accept", "application/json");
                connection.setRequestProperty("Connection", "keep-alive");
                connection.setRequestProperty("token", token);
                connection.setDoOutput(true);
                // Pass the parameters to the API
                String jsonStr = String.format(param.get("data").toString());
                CDTMSAPI.log.info("-----------------dataSave API  post body data is :" + jsonStr);
                // Write the request body
                try (OutputStream os = connection.getOutputStream()) {
                    byte[] input = jsonStr.getBytes(StandardCharsets.UTF_8);
                    os.write(input, 0, input.length);
                }
                int responseCode = connection.getResponseCode();
                String requestMethod = connection.getRequestMethod();
                CDTMSAPI.log.info("requestMethod is : " + requestMethod);
                CDTMSAPI.log.info("Response Code: " + responseCode);
                if (responseCode == HttpsURLConnection.HTTP_OK) {
                    BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    String inputLine;
                    StringBuilder response = new StringBuilder();
                    while ((inputLine = in.readLine()) != null) {
                        response.append(inputLine);
                    }
                    in.close();
                    // Convert response to JSONObject for parsing
                    CDTMSAPI.log.info("call CDTMSAPI.dataSave API response is :" + response.toString());
                }
            } catch (ProtocolException e) {
                throw new RuntimeException(e);
            } catch (MalformedURLException e) {
                throw new RuntimeException(e);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        } else {
            JSONObject jsonObject = new JSONObject(param.get("projectId").toString());
            String tableId = jsonObject.getStr("tableId");
            CDTMSAPI.usersyndataSave(taskId, tableId, formId, "", "", paramData);
        }


    }

    public static String usersyndataSave(String token, String tableid, String formid, String username, String format, String param) {
        StringBuilder response = new StringBuilder();
        String requestURL = "";
        if (format.isEmpty() || username.isEmpty()) {
            requestURL = SASOnlieConstant.REMOTE_SERVER_API + "usersyn/datasave?token=" + token + "&tableid=" + tableid + "&formid=" + formid;
        } else {
            requestURL = SASOnlieConstant.REMOTE_SERVER_API + "usersyn/datasave?token=" + token + "&tableid=" + tableid + "&formid=" + formid + "&username=" + username + "&format=" + format;
        }

        CDTMSAPI.log.info("---------------------------------------the dataSave request URL is :" + requestURL);
        try {
            URL url = new URL(requestURL);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.setDoOutput(true); // Set this before setting the request method
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Connection", "keep-alive");
            connection.setDoOutput(true);
            // Pass the parameters to the API
            String jsonStr = param;
            CDTMSAPI.log.info("-----------------usersyn dataSave API  post body data is :" + jsonStr);
            // Write the request body
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonStr.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
            int responseCode = connection.getResponseCode();
            String requestMethod = connection.getRequestMethod();
            CDTMSAPI.log.info("requestMethod is : " + requestMethod);
            CDTMSAPI.log.info("Response Code: " + responseCode);
            if (responseCode == HttpsURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;

                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                CDTMSAPI.log.info("call usersyn dataSave API response is :" + response.toString());

            }
        } catch (ProtocolException e) {
            throw new RuntimeException(e);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return response.toString();
    }


    //get info from cdtms
    public static String getInfo(String requestPrefix, String taskId, String projectId, String type) {
        log.info("the request projectid is:" + projectId + ",and taskId is :" + taskId + ",and type is :" + type);
        String data = "";
        String requestURL = requestPrefix + "remoteButtonTask/getInfo?taskId=" + taskId + "&projectId=" + projectId
                + "&type=" + type;
        try {
            URL url = new URL(requestURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            log.info("Response Code: " + responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                JSONObject jsonResponse = new JSONObject(response.toString());
                // Extract the study_id
                data = jsonResponse.get("data").toString();
                log.info("data: " + data);
                return data;
            } else {
                log.info("GET request not worked");
                return "falil";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }


    public static void updateWorkFlowStatus(String taskId, String projectId, String status, String statusValue) {
        CDTMSAPI.log.info("--------------------------------------------------调用了状态更新接口 !!!");
        Map<String, String> formInfoByTaskId = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        CDTMSAPI.log.info("--------------------------------------------------get formInfoByTaskId is :" + formInfoByTaskId);
        String data = formInfoByTaskId.get("param");
        String dataId = "";
        if (!data.isEmpty()) {
            JSONObject formInfoData = new JSONObject(data.toString());
            dataId = formInfoData.get("id").toString();
        }
        String formId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
        JSONObject param = new JSONObject();
        param.put("taskId", taskId);
        param.put("formId", formId);
        param.put("projectId", projectId);
        JSONObject temp = new JSONObject();
        temp.put(status, statusValue);
        temp.put("id", dataId);
        param.put("data", temp);
        CDTMSAPI.dataSave(param);
    }


    //随机生成表单formId
    public static String getFormIdByToken(String requestPrefix, String token) {
        String formid = "";
        String requestURL = requestPrefix + "usersyn/getformid?token=" + token;
        log.info("-----------------------requestURL is : " + requestURL);
        try {
            URL url = new URL(requestURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            log.info("Response Code: " + responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                JSONObject jsonResponse = new JSONObject(response.toString());
                // Extract the study_id
                formid = jsonResponse.get("formid").toString();
                log.info("formid: " + formid);
                return formid;
            } else {
                log.info("GET request not worked");
                return "falil";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return formid;
    }

    public static boolean isValidJson(String jsonString) {
        try {
            new JSONObject(jsonString);
            return true;
        } catch (Exception e) {
            return false;
        }
    }


    public static Map<String, String> getRTSMAccountEmail(String studyId) {
        Map<String, String> accountInfo = new HashMap<>();
        String craEmail = "";
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "crf_handover");
        String formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, token);
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        if (com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size() > 0) {
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            String result = CDTMSAPI.getDataListInfo(token, "cra", "obj.studyid='" + studyInt + "'" + "and obj.limitnum=5", "edit", "");
            craEmail = JSON.parseObject(JSON.parseArray(result).get(0).toString()).get("cra_email").toString();
            String userName = JSON.parseObject(JSON.parseArray(result).get(0).toString()).get("cra_name").toString();
            accountInfo.put("email", craEmail);
            accountInfo.put("name", userName);
        }
        return accountInfo;
    }


    public static Map<String, String> getRTSMAccountManagerEmail(String studyId) {
        Map<String, String> accountInfo = new HashMap<>();
        String craEmail = "";
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "crf_handover");
        String formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, token);
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        if (com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size() > 0) {
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            String result = CDTMSAPI.getDataListInfo(token, "cra", "obj.studyid='" + studyInt + "'" + "and obj.limitnum=6", "edit", "");
            craEmail = JSON.parseObject(JSON.parseArray(result).get(0).toString()).get("cra_email").toString();
            String userName = JSON.parseObject(JSON.parseArray(result).get(0).toString()).get("cra_name").toString();
            accountInfo.put("email", craEmail);
            accountInfo.put("name", userName);
        }

        return accountInfo;

    }


    public static Map<String, String> getStudyInfo(String studyId) {
        Map<String, String> accountInfo = new HashMap<>();
        String version = "";
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "crf_handover");
        String formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, token);
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        if (com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size() > 0) {
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            String result = CDTMSAPI.getDataListInfo(token, "xshtbbxx", "obj.studyid='" + studyInt + "'", "edit", "obj.revised_date desc");
            CDTMSAPI.log.info("-------------xshtbbxx的查询结果结果：", result);
            version = JSON.parseObject(JSON.parseArray(result).get(0).toString()).get("xsht_version").toString();
            String goal = JSON.parseObject(JSON.parseArray(result).get(0).toString()).get("study_goal").toString();
            String revised_date = JSON.parseObject(JSON.parseArray(result).get(0).toString()).get("revised_date").toString();
            String bbh = JSON.parseObject(JSON.parseArray(result).get(0).toString()).get("bbh").toString();

            accountInfo.put("caseVersionNum", version);
            accountInfo.put("title", goal);
            accountInfo.put("caseNum", bbh);
            accountInfo.put("caseVersionDate", revised_date);
        }

        return accountInfo;

    }


    public static String getStudyLanguage(String studyId) {
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "studyinfo");
        String dataListInfo = CDTMSAPI.getDataListInfo(token, "xsht", "obj.studyid='" + studyId + "') ", "edit", URLEncoder.encode("createtime desc"));
        com.alibaba.fastjson.JSONArray jsonArray = com.alibaba.fastjson.JSONArray.parseArray(dataListInfo);
        String tmp = jsonArray.get(0).toString();
        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(tmp);
        String original_language = jsonObject.get("used_language").toString();
        String used_language = "";
        if (original_language.equals("zh_CN")) {
            used_language = "CH";
        } else if (original_language.equals("en_US")) {
            used_language = "EN";
        }
        return used_language;
    }


    public static String getLoginUserRole(String studyId, String taskId) {
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "crf_handover");
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        String role = "";
        if (com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size() > 0) {
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            String result = CDTMSAPI.getDataListInfo(token, "roles", "obj.studyid='" + studyInt + "'" + "and obj.limitnum='TDM' and  obj.active='1' ", "", "");
            String resultA = CDTMSAPI.getDataListInfo(token, "roles", "obj.studyid='" + studyInt + "'" + "and obj.limitnum='DM' and  obj.active='1' ", "", "");
            String results = CDTMSAPI.getInfo(SASOnlieConstant.REMOTE_SERVER_API, taskId, SASOnlieConstant.REMOTE_SERVER_PROJECTID, "edit");
            com.alibaba.fastjson.JSONObject loginInfo = JSON.parseObject(results).getJSONObject("user");
            String userName = loginInfo.get("username").toString();
            com.alibaba.fastjson.JSONArray objects = com.alibaba.fastjson.JSONArray.parseArray(result);
            for (int i = 0; i < objects.size(); i++) {
                if (objects.getJSONObject(i).get("member").toString().equals(userName)) {
                    role = "TDM";
                    break;
                }
            }

            com.alibaba.fastjson.JSONArray objectsA = com.alibaba.fastjson.JSONArray.parseArray(resultA);
            for (int i = 0; i < objectsA.size(); i++) {
                if (objectsA.getJSONObject(i).get("member").toString().equals(userName)) {
                    role = "DM";
                    break;
                }
            }


        }
        return role;
    }


    public static String getCraNameAndEmail(String studyId, String roleValue, String tableId) {
        String accountInfo = "";
        String userName = "";
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, tableId);
        String formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, token);
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        if (com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size() > 0) {
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            String result = CDTMSAPI.getDataListInfo(token, "cra", "obj.studyid='" + studyInt + "'" + "and obj.limitnum=" + roleValue, "edit", "");
            com.alibaba.fastjson.JSONArray objects = JSON.parseArray(result);
            if (objects.size() > 0) {
                userName = JSON.parseObject(objects.get(0).toString()).get("cra_name").toString();
            }


            accountInfo = userName;
        }
        return accountInfo;
    }

    public static String getTDMName(String studyId) {
        String TDMName = "";
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, "5AF2E70E-1E6D-47B4-A9C5-3809448E11E8", "crf_handover");
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        if (com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size() > 0) {
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            //String result = CDTMSAPI.getDataListInfo(token, "roles", "obj.studyid='" + studyInt + "'", "edit", "obj.statr_date desc");
            String result = CDTMSAPI.getDataListInfo(token, "roles", "obj.studyid='" + studyInt + "'" + "and obj.limitnum='TDM' and  obj.active='1' ", "", "");
            com.alibaba.fastjson.JSONArray objects = com.alibaba.fastjson.JSONArray.parseArray(result);
            if (objects.size() > 0) {
                TDMName = JSON.parseObject(objects.get(0).toString()).get("member").toString();
            }
        }
        return TDMName;
    }

    public static String postContent(String urlStr, InputStream inputstream, int timeOut, int byteSize) throws Exception {
        log.info("------------------------------调用的上传接口地址urlStr: " + urlStr + "-------------------------------------------------");
        URL url = new URL(urlStr);
        HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
        httpConn.setDoInput(true);
        httpConn.setDoOutput(true);
        httpConn.setRequestMethod("POST");
        httpConn.setConnectTimeout(timeOut);
        httpConn.setReadTimeout(timeOut);
        httpConn.setRequestProperty("content-type", "application/octet-stream");

        httpConn.connect();
        int len = 0;
        byte[] byteA = new byte[byteSize];
        OutputStream outStream = httpConn.getOutputStream();
        while ((len = inputstream.read(byteA)) > 0) {
            outStream.write(byteA, 0, len);
        }
        httpConn.getOutputStream().flush();
        httpConn.getOutputStream().close();

        OutputStream os = new ByteArrayOutputStream();

        InputStream is = httpConn.getInputStream();
        byte[] bA = new byte[byteSize];
        len = is.read(bA);
        while (len > 0) {
            os.write(bA, 0, len);
            len = is.read(bA);
        }


        os.close();
        is.close();

        int responseCode = httpConn.getResponseCode();
        String responseMessage = httpConn.getResponseMessage();

        log.info("-------------------------------call cdtms upload API responseCode is :" + responseCode);
        log.info("-------------------------------call cdtms upload API responseMessage is :" + responseMessage);
        log.info("-------------------------------call cdtms upload API responseBody is :" + os.toString());

        return os.toString();
    }

    public static String uploadFileByUsersyn(String recordid, String formId, String fid, String filePath,
                                             String tableId, String urlPrefix, String fn,
                                             String dataType) {
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, tableId);
        formId = CDTMSAPI.getFormIdByToken(SASOnlieConstant.REMOTE_SERVER_API, token);
        log.info("--------------------------上传sas输出文件到CDTMS开始！！！-------------------------------------------");
        File file = new File(filePath);
        String fileName = file.getName();
        String urlString = "";
        log.info("-----------------需要上传的本地文件名：" + fileName + "--------------------------------------------------");
        log.info("-----------------需要上传的原始文件名：" + fn + "--------------------------------------------------");
        try {
            urlString = "https://" + urlPrefix + "usersyn/upload"
                    + "?token=" + token + "&formid=" + formId + "&tableid=" + tableId + "&fid=" + fid + "&recordid=" + recordid + "&fn=" + URLEncoder.encode(fn, "UTF8");
        } catch (UnsupportedEncodingException e) {
            log.error("------------------地址栏文件名URLEncoder异常!!!---------------------------------------------------");
            throw new RuntimeException(e);
        }


        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }
        String ret = null;
        try {
            //call cdtms API to upload file
            if (dataType.equals("edc_account_history")) {
                ret = postContent(urlString, fis, 600000, 524288);
            } else  if(dataType.equals("Subject_CRF")){
                ret = postContent(urlString, fis, 600000, 524288);
            }else {
                ret = postContent(urlString, fis, 15000, 8192);
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        log.info(ret);
        String ufn="";
        JSONObject jsonResponse = new JSONObject(ret.toString());
        // Extract the study_id
        String data = jsonResponse.get("ufn").toString();
        if (!ObjectUtils.isEmpty(data) && !data.isEmpty()) {
            ufn = data;
        }
        return ufn;


    }




    public static String uploadFileByRemoteAPI(String taskId, String formId, String fid, String filePath,
                                               String projectId, String urlPrefix, String newFileName,
                                               String dataType) {
        log.info("--------------------------上传sas输出文件到CDTMS开始！！！-------------------------------------------");
        File file = new File(filePath);
        String fileName = file.getName();
        String urlString = "";
        log.info("-----------------需要上传的本地文件名：" + fileName + "--------------------------------------------------");
        log.info("-----------------需要上传的原始文件名：" + newFileName + "--------------------------------------------------");
        try {
            urlString = "https://" + urlPrefix + "remoteButtonTask/upload"
                    + "?taskId=" + taskId + "&formId=" + formId + "&fid=" + URLEncoder.encode(fid, "UTF8") + "&fn=" + URLEncoder.encode(newFileName, "UTF8") + "&projectId=" + projectId;
        } catch (UnsupportedEncodingException e) {
            log.error("------------------地址栏文件名URLEncoder异常!!!---------------------------------------------------");
            throw new RuntimeException(e);
        }


        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }

        String ufn = "";
        String ret = null;
        try {
            //call cdtms API to upload file
            if (dataType.equals("edc_account_history")) {
                ret = postContent(urlString, fis, 600000, 524288);
            } else {
                ret = postContent(urlString, fis, 15000, 8192);
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        JSONObject jsonResponse = new JSONObject(ret.toString());
        // Extract the study_id
        String data = jsonResponse.get("data").toString();
        if (!ObjectUtils.isEmpty(data) && !data.isEmpty()) {
            JSONObject dataObj = new JSONObject(data);
            ufn = dataObj.get("ufn").toString();
        }
        return ufn;
    }




    public static String getUfn(String filePath,String taskId,String projectId){
        String ufn="";
        if(!filePath.isEmpty()){
            File file = new File(filePath);
            if(file.exists()){
                //回传文件
                String latestFormId = CDTMSAPI.getFormIdByTaskId(taskId, projectId);
                ufn = uploadFileByRemoteAPI(taskId,latestFormId,file.getName(),filePath,SASOnlieConstant.REMOTE_SERVER_PROJECTID,SASOnlieConstant.REMOTE_SERVER_API_PREFIX,file.getName(),"csv");

            }
        }
        return ufn;
    }


    public static String getUfnByToken(String filePath,String dataId,String fid,String dataType,String tableId){
        String ufn="";
        if(!filePath.isEmpty()){
            File file = new File(filePath);
            if(file.exists()){
                //回传文件
                String latestFormId = CDTMSAPI.getFormIdByTaskId(dataId, "cdtmsen_val");
                ufn = uploadFileByUsersyn(dataId,latestFormId,fid,filePath,tableId,SASOnlieConstant.REMOTE_SERVER_API,file.getName(),dataType);

            }
        }
        return ufn;
    }

    
    public static String callCodingFullfill(String recordId){
        String result="";
        try {
            // 定义 URL，将 3952738304 作为变量传入
            String apiUrl =SASOnlieConstant.MEDCODING_PLAN+recordId;
            log.info("--------------------请求的回填的接口地址apiUrl: " + apiUrl);
            URL url = new URL(apiUrl);

            // 打开连接
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setRequestProperty("accept", "application/json");

            // 获取响应状态码
            int responseCode = connection.getResponseCode();
            log.info("Response Code: " + responseCode);

            // 读取响应内容
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();

                // 打印响应结果
                log.info("Response: " + response.toString());
                result= response.toString();
            } else {
                log.info("GET request failed");
                result="GET request failed";

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }



    public static cn.hutool.json.JSONObject getRequest(String param) {
        String requestURL = param;
        try {
            URL url = new URL(requestURL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            log.info("Response Code: " + responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                // Convert response to JSONObject for parsing
                cn.hutool.json.JSONObject jsonResponse = new cn.hutool.json.JSONObject(response.toString());
                // Extract the study_id
                if (!ObjectUtils.isEmpty(jsonResponse.getJSONObject("data"))) {
                    return jsonResponse.getJSONObject("data");
                }

            } else {
                log.info("GET request not worked");
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
