package com.hengrui.medcoding.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hengrui.medcoding.constant.SASOnlieConstant;
import com.hengrui.medcoding.service.DownloadMedcoding;
import com.hengrui.medcoding.service.MedCodingService;
import com.hengrui.medcoding.utils.CDTMSAPI;
import com.hengrui.medcoding.utils.FilesUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import static com.hengrui.medcoding.service.DownloadMedcoding.TEMP_FILE_PATH;

/**
 * @ClassName MedCodingServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/27 9:53
 * @Version 1.0
 **/
@Service
@Slf4j
public class MedCodingServiceImpl  implements MedCodingService {
    @Autowired
    DownloadMedcoding downloadMedcoding;


    @Override
    public Map<String, String> getMedCodingFile(String taskId, String projectId, String server) {
        //调用下载逻辑
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String studyId = formInfo.get("studyId");
        downloadMedcoding.downloadCodingFileBystudyId(studyId,taskId,projectId);
        return null;
    }

    @Override
    public Map<String, String> putMedCodingFile(String taskId, String projectId, String server) {
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String studyId = formInfo.get("studyId");
        downloadMedcoding.downloadCodingFileBystudyId2(studyId,taskId,projectId);
        return null;
    }

    @Override
    public String  getMedCodingFiles(String taskId, String projectId, String server) {
        String msg = "";
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);
        String studyId = formInfo.get("studyId");
        String data = formInfo.get("param");
        String dataId = "";
        if (!data.isEmpty()) {
            JSONObject formInfoData = JSONObject.parseObject(data);
            dataId = formInfoData.get("id").toString();
        }
        //获取表单最新的文件
        String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "study_coding_plan");
        String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
        if (com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size() > 0) {
            String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
            String result = CDTMSAPI.getDataListInfo(token, "coding", "obj.studyid='" + studyInt + "'", "edit", "");
            if (com.alibaba.fastjson.JSONArray.parseArray(result).size() > 0) {
                String drugmsg="";
                String medrug = com.alibaba.fastjson.JSONArray.parseArray(result).getJSONObject(0).get("code_doc").toString();
                String whoDrug = com.alibaba.fastjson.JSONArray.parseArray(result).getJSONObject(0).get("whodrugcoding").toString();

                if (medrug != null && !"".equals(medrug)) {
                    //获取ufn
                    String input = medrug;
                    // 获取文件名和文件索引
                    String[] fileEntries = input.split("\\|");
                    for (String entry : fileEntries) {
                        if (entry.isEmpty())
                            continue;
                                String[] parts = entry.split("\\*");
                                if (parts.length == 2) {
                                    String fileName = parts[0];
                                    String fileIndex = parts[1];
                                    log.info("File Name: " + fileName);
                                    log.info("File Index: " + fileIndex);
                                    try {
                                        CDTMSAPI.downloadDataByUserSync("coding",token,fileIndex,TEMP_FILE_PATH+fileName);
                                    } catch (IOException e) {
                                        throw new RuntimeException(e);
                                    }
                                }
                    }


                }else{
                    drugmsg = "该项目最新编码记录未上传MedDRA结果!";
                }
                if (whoDrug != null && !"".equals(whoDrug)) {
                    //获取ufn
                    String input = whoDrug;
                    // 获取文件名和文件索引
                    String[] fileEntries = input.split("\\|");
                    for (String entry : fileEntries) {
                        if (entry.isEmpty())
                            continue;
                        String[] parts = entry.split("\\*");
                        if (parts.length == 2) {
                            String fileName = parts[0];
                            String fileIndex = parts[1];
                            log.info("File Name: " + fileName);
                            log.info("File Index: " + fileIndex);
                            try {
                                CDTMSAPI.downloadDataByUserSync("coding",token,fileIndex,TEMP_FILE_PATH+fileName);
                            } catch (IOException e) {
                                throw new RuntimeException(e);
                            }

                        }
                    }

                }else{
                    drugmsg = drugmsg+" 该项目最新编码记录未上传WHODrug结果!";
                }
                if(!drugmsg.isEmpty()){
                    msg= drugmsg;
                }else{
                    msg = "success!";
                }



            }
        }else{
            msg = "未找到该项目进行阶段的编码记录";
        }

        JSONObject formInfoData = JSONObject.parseObject(data);
        String codingFile = formInfoData.get("coding_plan").toString();
        log.info("获取到的医学编码计划的文件名称是:" + codingFile);
        if (!ObjectUtils.isEmpty(codingFile) && !codingFile.isEmpty()) {
            String fullfillResult = CDTMSAPI.callCodingFullfill(dataId);
            log.info("-----------------编码文件内容回填计划接口调用的结果是:" + fullfillResult);
        }

        return msg;
    }

    @Override
    public String getMedCodingUTR(String taskId, String projectId,String server) {
        //1.获取当前表单记录的medra 和whodrug附件,上传到minio上，添加相应的tag
        Map<String, String> formInfo = CDTMSAPI.getFormInfoByTaskId(taskId, projectId);

        String recordId = "";
        String tableId = "";
        if (!ObjectUtils.isEmpty(formInfo.get("recordId"))) {
            recordId = formInfo.get("recordId");
            tableId = formInfo.get("tableId");
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(projectId);
            com.alibaba.fastjson.JSONObject formData = object.getJSONObject("formData");
            formData.put("id", recordId);
            object.put("formData", formData);
            projectId = object.toJSONString();
        } else {
            tableId = projectId;
            recordId = taskId;
        }
        //1.get the  studyId
        String studyId = formInfo.get("studyId");
        String data = formInfo.get("param").toString();
        String dataId = "";
        if (!data.isEmpty()) {

            //2.获取除当前记录最新的已锁定的medra 和whodrug附件,上传到minio上，添加相应的tag
            String token = CDTMSAPI.getToken(SASOnlieConstant.REMOTE_SERVER_API, SASOnlieConstant.REMOTE_SERVER_PROJECTID, SASOnlieConstant.SECRET_KEY, "esign_account");
            String studyIdNum = CDTMSAPI.getDataListInfo(token, "Xsht", "obj.studyid='" + studyId + "'", "edit", "");
            if (com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).size() > 0) {
                String studyInt = com.alibaba.fastjson.JSONArray.parseArray(studyIdNum).getJSONObject(0).get("id").toString();
                String result = CDTMSAPI.getDataListInfo(token, "coding", "obj.id!='" + dataId + "' and obj.zt='10' and obj.studyid='" + studyInt + "'", "edit", "");
                JSONArray objects = JSONArray.parseArray(result);
                //锁定版本次新的记录附件
                String codeDocFN = objects.getJSONObject(0).get("code_doc").toString();
                String whodrugFN = objects.getJSONObject(0).get("whodrugcoding").toString();
                //3.调用sas程序，回传sas输出结果到表单对应的附件字段
                if (objects.size() > 1&&(null!=codeDocFN||null!=whodrugFN)) {
                        //获取对应的附件
                    if (!ObjectUtils.isEmpty(codeDocFN) || !codeDocFN.isEmpty()) {
                        //下载文件上传到Minio
                        List<String> medDRAFilePaths = FilesUtil.downloadFilesByFN("coding",codeDocFN, ".zip");
                        downloadMedcoding.uploadFormMedFileToMinioNoJudge(medDRAFilePaths, studyId);
                    }
                    if (!ObjectUtils.isEmpty(whodrugFN) || !whodrugFN.isEmpty()) {
                        //下载文件上传到Minio
                        List<String> medDRAFilePaths = FilesUtil.downloadFilesByFN("coding",whodrugFN,".zip");
                        downloadMedcoding.uploadFormMedFileToMinioNoJudge(medDRAFilePaths, studyId);
                    }
                }else{
                    return "无次新的UTR报告记录";
                }
            }else{
                return "项目代码无效";
            }


            cn.hutool.json.JSONObject formInfoData = new cn.hutool.json.JSONObject(data);
            cn.hutool.json.JSONObject params = new cn.hutool.json.JSONObject();
            dataId = formInfoData.get("id").toString();
            String medraCoding = formInfoData.get("code_doc").toString();
            String whodrugCoding = formInfoData.get("whodrugcoding").toString();

            if (!ObjectUtils.isEmpty(medraCoding) || !medraCoding.isEmpty()) {
                //下载文件上传到Minio
                List<String> medDRAFilePaths = FilesUtil.downloadFiles(taskId, "code_doc", ".zip");
                downloadMedcoding.uploadFormMedFileToMinioNoJudge(medDRAFilePaths, studyId);
            }
            if (!ObjectUtils.isEmpty(whodrugCoding) || !whodrugCoding.isEmpty()) {
                //下载文件上传到Minio
                List<String> medDRAFilePaths = FilesUtil.downloadFiles(taskId, "whodrugcoding", ".zip");
                downloadMedcoding.uploadFormMedFileToMinioNoJudge(medDRAFilePaths, studyId);
            }

            cn.hutool.json.JSONObject request = CDTMSAPI.getRequest( "http://localhost:8087/sas_online/getMedCodingUTR?taskId=" + taskId + "&server=" + server + "&projectId=" + projectId);
            return request.get("msg").toString();
        }
        return "";
    }
}







