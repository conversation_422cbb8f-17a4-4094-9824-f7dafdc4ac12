package com.hengrui.medcoding.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Component
@Mapper
@Repository
@DS("master")
public interface MedcodingMapper {
    List<Map<String,String>> getWhoDrugBatchInfo(String studyId);

    List<Map<String,String>> getMedDrugBatchInfo(String studyId);

    List<String> getAllStudyIds();

    String getMedProjectLangType(String studyId);

    String getWhoProjectLangType(String studyId);
}
