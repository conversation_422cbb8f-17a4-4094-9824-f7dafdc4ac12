server.port=8088
server.servlet.context-path=/medcoding_download
preview-prfix=http://10.10.5.74:
#spring.datasource.url=************************************************************************************************************* = Asia/Shanghai
#spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.datasource.username=weix5
#spring.datasource.password=weix@edc
spring.datasource.url=************************************************************************************************************* = Asia/Shanghai
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.username=weix5
spring.datasource.password=weix@hr


mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.auto-mapping-behavior=full
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.mapper-locations=classpath*:/mapper/*.xml
mybatis-plus.type-aliases-package=org.susarauto.login.bean

# Minio配置
minio.url=http://10.10.5.75:9000
minio.access-key=minioadmin
minio.secret-key=minioadmin
minio.bucket-name=coding