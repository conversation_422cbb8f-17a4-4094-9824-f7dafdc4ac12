package com.hengrui.medcoding.controller;

import com.hengrui.medcoding.service.MedCodingService;
import com.hengrui.medcoding.utils.FilesUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName MedCodingController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/27 9:46
 * @Version 1.0
 **/

@RestController
@Slf4j
public class MedCodingController {

    @Autowired
    MedCodingService medCodingService;

    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getMedCodingFiles")
    public Map<String, Object> getMedCodingFile(String taskId,
                                              String server,
                                              String projectId) {
        log.info("server is :" + server);
        FilesUtil.getCDTMSAPI(server);
        FilesUtil.getCDTMSAPIProjectId(server);
        FilesUtil.getCDTMSAPIPre(server);
        Map<String, String> results = medCodingService.getMedCodingFile(taskId, projectId, server);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        return result;
    }



    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/putMedCodingFile")
    public Map<String, Object> putMedCodingFile(String taskId,
                                                String server,
                                                String projectId) {
        log.info("server is :" + server);
        FilesUtil.getCDTMSAPI(server);
        FilesUtil.getCDTMSAPIProjectId(server);
        FilesUtil.getCDTMSAPIPre(server);
        Map<String, String> results = medCodingService.putMedCodingFile(taskId, projectId, server);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", "success");
        return result;
    }



    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getMedCodingFile")
    public Map<String, Object> getMedCodingFiles(String taskId,
                                                String server,
                                                String projectId) {
        log.info("server is :" + server);
        FilesUtil.getCDTMSAPI(server);
        FilesUtil.getCDTMSAPIProjectId(server);
        FilesUtil.getCDTMSAPIPre(server);
        String  msg = medCodingService.getMedCodingFiles(taskId, projectId, server);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", msg);
        return result;
    }


    @CrossOrigin(origins = "*", maxAge = 3600)
    @GetMapping("/getMedCodingUTR")
    @ResponseBody
    public Map<String, Object> getMedCodingUTR(String taskId,
                                               String server,
                                               String projectId) {
        FilesUtil.getCDTMSAPI(server);
        FilesUtil.getCDTMSAPIProjectId(server);
        FilesUtil.getCDTMSAPIPre(server);
        String results = medCodingService.getMedCodingUTR(taskId, projectId,server);
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("code", 200);
        result.put("msg", results);
        return result;
    }


}
